{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">سجل المخالفات</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item active" aria-current="page">سجل المخالفات</li>
            </ol>
        </nav>
    </div>
    <div>
        {% if current_user.has_permission('add_violation') %}
        <a href="{{ url_for('new_violation') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> إضافة مخالفة جديدة
        </a>
        {% endif %}
        {% if current_user.has_permission('import_violation_types') %}
        <a href="{{ url_for('violation_types_list') }}" class="btn btn-secondary">
            <i class="fas fa-list me-1"></i> أنواع المخالفات
        </a>
        {% endif %}
    </div>
</div>

<!-- تم إخفاء البطاقات الإحصائية -->

<!-- بحث وتصفية -->
<div class="card shadow-sm mb-4">
    <div class="card-body">
        <form method="get" action="{{ url_for('violations_clubs_list') }}">
            <div class="row align-items-end">
                <div class="col-md-10">
                    <label for="search" class="form-label">بحث في الأندية</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="اسم النادي..." value="{{ search_query }}">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i> بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الأندية -->
{% if clubs_with_stats %}
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-building me-2"></i> قائمة الأندية</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle mb-0">
                <thead class="table-primary text-white">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th width="25%">اسم النادي</th>
                        <th class="text-center" width="10%">الموظفين</th>
                        <th class="text-center" width="10%">إجمالي المخالفات</th>
                        <th class="text-center" width="10%">{{ current_month_name }}</th>
                        <th class="text-center" width="10%">موقعة</th>
                        <th class="text-center" width="10%">غير موقعة</th>
                        <th class="text-center" width="20%">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for club_data in clubs_with_stats %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-building text-primary me-2"></i>
                                <strong>{{ club_data.club.name }}</strong>
                            </div>
                        </td>
                        <td class="text-center">
                            {% if club_data.stats.employees_count > 0 %}
                                <span class="badge bg-info">{{ club_data.stats.employees_count }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if club_data.stats.total_violations > 0 %}
                                <span class="badge bg-warning text-dark">{{ club_data.stats.total_violations }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if club_data.stats.current_month_violations > 0 %}
                                <span class="badge bg-danger">{{ club_data.stats.current_month_violations }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if club_data.stats.signed_violations > 0 %}
                                <span class="badge bg-success">{{ club_data.stats.signed_violations }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if club_data.stats.unsigned_violations > 0 %}
                                <span class="badge bg-secondary">{{ club_data.stats.unsigned_violations }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                <a href="{{ url_for('violations_club_detail', club_id=club_data.club.id) }}" class="btn btn-sm btn-primary" title="عرض موظفي النادي">
                                    <i class="fas fa-users me-1"></i> عرض الموظفين
                                </a>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="تصدير Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" style="min-width: 300px;">
                                        <li><h6 class="dropdown-header">تصدير المخالفات</h6></li>
                                        <li><a class="dropdown-item" href="{{ url_for('export_violations_excel', club_id=club_data.club.id) }}">
                                            <i class="fas fa-download me-1"></i> جميع المخالفات
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ url_for('export_violations_excel', club_id=club_data.club.id, month='current') }}">
                                            <i class="fas fa-calendar-alt me-1"></i> الشهر الحالي
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><h6 class="dropdown-header">تصدير شهر محدد</h6></li>
                                        <li class="px-3 py-2">
                                            <form method="GET" action="{{ url_for('export_violations_excel', club_id=club_data.club.id) }}" class="export-form">
                                                <div class="row g-2">
                                                    <div class="col-7">
                                                        <select class="form-select form-select-sm" name="specific_month" required>
                                                            <option value="">اختر الشهر</option>
                                                            <option value="1">يناير</option>
                                                            <option value="2">فبراير</option>
                                                            <option value="3">مارس</option>
                                                            <option value="4">أبريل</option>
                                                            <option value="5">مايو</option>
                                                            <option value="6">يونيو</option>
                                                            <option value="7">يوليو</option>
                                                            <option value="8">أغسطس</option>
                                                            <option value="9">سبتمبر</option>
                                                            <option value="10">أكتوبر</option>
                                                            <option value="11">نوفمبر</option>
                                                            <option value="12">ديسمبر</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-5">
                                                        <select class="form-select form-select-sm" name="year" required>
                                                            <option value="">العام</option>
                                                            {% set current_year = 2024 %}
                                                            {% for year in range(2020, 2030) %}
                                                                <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <button type="submit" class="btn btn-primary btn-sm w-100">
                                                        <i class="fas fa-download me-1"></i> تصدير
                                                    </button>
                                                </div>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                                {% if current_user.has_permission('add_violation') %}
                                <a href="{{ url_for('new_violation') }}" class="btn btn-sm btn-warning" title="إضافة مخالفة جديدة">
                                    <i class="fas fa-plus"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
    <div class="card">
        <div class="card-body">
            <div class="text-center py-5">
                <i class="fas fa-building fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أندية للعرض</h5>
                <p class="text-muted">لا يوجد لديك أندية متاحة أو لا توجد أندية تطابق البحث</p>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block head %}
<style>
    /* تحسين مظهر البطاقات */
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: box-shadow 0.15s ease-in-out;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* تحسين مظهر الشارات */
    .badge {
        font-size: 0.75rem;
        padding: 0.4em 0.6em;
        border-radius: 0.375rem;
    }

    /* تحسين مظهر الجداول */
    .table th {
        font-weight: 600;
        font-size: 0.875rem;
        border-bottom: 2px solid #dee2e6;
    }

    /* تخصيص رأس الجدول الأزرق */
    .table-primary {
        background-color: #007bff !important;
    }

    .table-primary th {
        background-color: #007bff !important;
        color: white !important;
        border-color: #0056b3 !important;
    }

    .table td {
        vertical-align: middle;
        font-size: 0.875rem;
    }

    /* تحسين مظهر الأزرار */
    .btn-group .btn {
        border-radius: 0.25rem;
        margin: 0 1px;
        transition: all 0.2s ease;
    }

    .btn-group .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    /* تحسين أزرار التصدير */
    .btn-success.dropdown-toggle {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
    }

    .btn-success.dropdown-toggle:hover {
        background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    }

    /* تحسين القائمة المنسدلة */
    .dropdown-menu {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.375rem;
    }

    .dropdown-item {
        transition: all 0.2s ease;
    }

    .dropdown-item:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        transform: translateX(5px);
    }

    /* تحسين نموذج التصدير */
    .export-form {
        background: #f8f9fc;
        border-radius: 0.375rem;
        padding: 0.5rem;
        margin: 0;
    }

    .export-form .form-select-sm {
        font-size: 0.75rem;
        border: 1px solid #d1d3e2;
        border-radius: 0.25rem;
    }

    .export-form .form-select-sm:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .export-form .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .export-form .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 0.25rem 0.5rem rgba(102, 126, 234, 0.3);
    }

    .export-form .btn-secondary {
        background: #6c757d;
        border: none;
        font-size: 0.75rem;
        font-weight: 600;
        cursor: not-allowed;
    }

    .export-form .btn.disabled {
        opacity: 0.6;
        pointer-events: none;
    }

    /* تحسين رؤوس القائمة المنسدلة */
    .dropdown-header {
        font-size: 0.75rem;
        font-weight: 600;
        color: #495057;
        background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
        border-radius: 0.25rem;
        margin: 0.25rem 0.5rem;
        padding: 0.5rem;
    }

    /* تحسين الفواصل */
    .dropdown-divider {
        margin: 0.5rem 0;
        border-color: #e9ecef;
    }

    /* تحسين مظهر الصفوف */
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    /* تحسين مظهر بطاقات الملخص */
    .card.bg-primary,
    .card.bg-info,
    .card.bg-warning,
    .card.bg-danger {
        border: none;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }

    .card.bg-primary:hover,
    .card.bg-info:hover,
    .card.bg-warning:hover,
    .card.bg-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // إضافة تأثيرات hover للبطاقات
        $('.card.bg-primary, .card.bg-info, .card.bg-warning, .card.bg-danger').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // إضافة tooltip للشارات
        $('[title]').tooltip();

        // تحسين تفاعل الجدول
        $('.table tbody tr').hover(
            function() {
                $(this).addClass('table-active');
            },
            function() {
                $(this).removeClass('table-active');
            }
        );

        // تحسين أزرار التصدير
        $('.dropdown-item').on('click', function(e) {
            const $this = $(this);
            const originalText = $this.html();

            // إضافة تأثير تحميل
            $this.html('<i class="fas fa-spinner fa-spin me-1"></i> جاري التصدير...');
            $this.addClass('disabled');

            // استعادة النص الأصلي بعد 3 ثوان
            setTimeout(function() {
                $this.html(originalText);
                $this.removeClass('disabled');
            }, 3000);
        });

        // تحسين تفاعل القائمة المنسدلة
        $('.dropdown-toggle').on('show.bs.dropdown', function() {
            $(this).addClass('shadow-lg');
        }).on('hide.bs.dropdown', function() {
            $(this).removeClass('shadow-lg');
        });

        // إضافة تأثير تحميل للنماذج
        $('.export-form').on('submit', function() {
            const $btn = $(this).find('button[type="submit"]');
            const originalText = $btn.html();
            $btn.html('<i class="fas fa-spinner fa-spin me-1"></i> جاري التصدير...');
            $btn.prop('disabled', true);

            // استعادة النص الأصلي بعد 5 ثوان (في حالة عدم نجاح التحميل)
            setTimeout(function() {
                $btn.html(originalText);
                $btn.prop('disabled', false);
            }, 5000);
        });
    });
</script>
{% endblock %}
