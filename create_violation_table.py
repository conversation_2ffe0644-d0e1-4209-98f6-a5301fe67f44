#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لإنشاء جدول violation
"""

import sqlite3
from datetime import datetime

def create_violation_table():
    """إنشاء جدول violation"""
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('area_manager.db')
    cursor = conn.cursor()
    
    try:
        print("جاري إنشاء جدول violation...")
        
        # التحقق من وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='violation'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("الجدول موجود بالفعل")
            return
        
        # إنشاء جدول violation
        cursor.execute("""
            CREATE TABLE violation (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                violation_type_id INTEGER NOT NULL,
                violation_date DATE NOT NULL,
                description TEXT,
                penalty_amount FLOAT DEFAULT 0.0,
                status VARCHAR(20) DEFAULT 'pending',
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employee (id),
                FOREIGN KEY (violation_type_id) REFERENCES violation_type (id),
                FOREIGN KEY (created_by) REFERENCES user (id)
            )
        """)
        
        print("✓ تم إنشاء جدول violation بنجاح")
        
        # إضافة بعض البيانات التجريبية
        print("جاري إضافة بيانات تجريبية...")
        
        # التحقق من وجود موظفين
        cursor.execute("SELECT id FROM employee LIMIT 5")
        employees = cursor.fetchall()
        
        if employees:
            # إضافة مخالفات تجريبية
            test_violations = [
                (employees[0][0], 1, '2025-06-25', 'تأخر عن العمل لمدة 30 دقيقة', 50.0, 'pending', 'مخالفة أولى', 1),
                (employees[0][0], 2, '2025-06-20', 'لم يرتدي الزي الموحد', 25.0, 'resolved', 'تم التنبيه', 1),
            ]
            
            if len(employees) > 1:
                test_violations.extend([
                    (employees[1][0], 3, '2025-06-22', 'استخدم الهاتف أثناء العمل', 30.0, 'pending', 'تم رصده من قبل المشرف', 1),
                    (employees[1][0], 1, '2025-06-18', 'تأخر عن العمل', 50.0, 'resolved', 'دفع الغرامة', 1),
                ])
            
            for violation in test_violations:
                cursor.execute("""
                    INSERT INTO violation (employee_id, violation_type_id, violation_date, description, 
                                         penalty_amount, status, notes, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, violation)
            
            print(f"✓ تم إضافة {len(test_violations)} مخالفة تجريبية")
        else:
            print("لا توجد موظفين في قاعدة البيانات لإضافة مخالفات تجريبية")
        
        # حفظ التغييرات
        conn.commit()
        print("✓ تم حفظ التغييرات بنجاح")
        
        # التحقق من النتيجة
        cursor.execute("SELECT COUNT(*) FROM violation")
        count = cursor.fetchone()[0]
        print(f"عدد المخالفات في الجدول: {count}")
        
        # عرض هيكل الجدول
        cursor.execute("PRAGMA table_info(violation)")
        columns = cursor.fetchall()
        print("\nهيكل جدول violation:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - {'PK' if col[5] else ''}")
        
        # اختبار العلاقة مع violation_type
        print("\n=== اختبار العلاقة مع violation_type ===")
        cursor.execute("""
            SELECT v.id, v.description, vt.name, vt.penalty_amount
            FROM violation v
            JOIN violation_type vt ON v.violation_type_id = vt.id
            LIMIT 3
        """)
        
        results = cursor.fetchall()
        if results:
            print("✓ العلاقة تعمل بشكل صحيح:")
            for result in results:
                print(f"  مخالفة {result[0]}: {result[1]} - نوع: {result[2]} - غرامة: {result[3]}")
        else:
            print("لا توجد مخالفات للاختبار")
        
    except Exception as e:
        print(f"حدث خطأ: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    create_violation_table()
