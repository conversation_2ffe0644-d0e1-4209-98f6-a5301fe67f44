-- حفظ البيانات القديمة
CREATE TABLE user_backup AS SELECT * FROM user;

-- حذ<PERSON> الجدول القديم
DROP TABLE user;

-- إنشاء الجدول الجديد بالعمود الجديد
CREATE TABLE user (
    id INTEGER NOT NULL PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(64),
    name VA<PERSON><PERSON><PERSON>(100),
    phone VARCHAR(20),
    password VARCHAR(128),
    is_admin BOOLEAN,
    role VARCHAR(50) DEFAULT 'user',
    profile_image VARCHAR(255) DEFAULT 'default.jpg'
);

-- إعادة البيانات القديمة
INSERT INTO user (id, username, name, phone, password, is_admin, role)
SELECT id, username, name, phone, password, is_admin, role
FROM user_backup;

-- حذ<PERSON> الجدول المؤقت
DROP TABLE user_backup;
