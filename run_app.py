from app import app, db

if __name__ == '__main__':
    with app.app_context():
        try:
            db.create_all()
            print("تم تهيئة قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
            
    print("\nجاري تشغيل التطبيق...")
    print("يمكنك الوصول إلى التطبيق على:")
    print("http://localhost:5000")
    print("http://127.0.0.1:5000")
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        print("تأكد من أن المنفذ 5000 غير مستخدم من قبل تطبيق آخر")
        print(f'http://127.0.0.1:{port}')
        print('\nاضغط CTRL+C لإيقاف الخادم')
        
        app.run(host='127.0.0.1', port=port, debug=True)
    else:
        print('لم يتم العثور على منفذ متاح!')
