from app import app, db
import os
import socket

def find_free_port():
    """البحث عن منفذ متاح للاستخدام"""
    ports = [5000, 8080, 3000, 8000]
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('127.0.0.1', port))
            sock.close()
            return port
        except:
            continue
    return 5000  # العودة إلى المنفذ الافتراضي إذا لم يتم العثور على منفذ متاح

if __name__ == '__main__':
    # إنشاء قاعدة البيانات إذا لم تكن موجودة
    with app.app_context():
        try:
            db.create_all()
            print("تم التحقق من قاعدة البيانات بنجاح!")
        except Exception as e:
            print(f"خطأ في قاعدة البيانات: {str(e)}")

    # البحث عن منفذ متاح
    port = find_free_port()
    
    print("\nجاري تشغيل التطبيق...")
    print(f"يمكنك الوصول إلى التطبيق على: http://127.0.0.1:{port}")
    print("اضغط CTRL+C لإيقاف الخادم\n")
    
    # تشغيل التطبيق
    app.run(
        host='127.0.0.1',  # localhost
        port=port,         # المنفذ المتاح
        debug=True        # وضع التطوير
    )
