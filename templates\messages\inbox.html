{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-inbox me-2"></i>
                    صندوق الوارد
                    {% if unread_count > 0 %}
                        <span class="badge bg-danger ms-2">{{ unread_count }}</span>
                    {% endif %}
                </h2>
                <div>
                    {% if current_user.has_permission('send_message') %}
                        <a href="{{ url_for('compose_message') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            رسالة جديدة
                        </a>
                    {% endif %}
                    <a href="{{ url_for('messages_sent') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-paper-plane"></i>
                        الرسائل المرسلة
                    </a>
                </div>
            </div>

            <!-- شريط البحث والتصفية -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search_query }}" placeholder="البحث في الموضوع أو المحتوى أو المرسل">
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">نوع الرسالة</label>
                            <select class="form-select" id="type" name="type">
                                <option value="all" {% if message_type == 'all' %}selected{% endif %}>جميع الأنواع</option>
                                <option value="general" {% if message_type == 'general' %}selected{% endif %}>عامة</option>
                                <option value="notification" {% if message_type == 'notification' %}selected{% endif %}>إشعار</option>
                                <option value="alert" {% if message_type == 'alert' %}selected{% endif %}>تنبيه</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" {% if status == 'all' %}selected{% endif %}>جميع الرسائل</option>
                                <option value="unread" {% if status == 'unread' %}selected{% endif %}>غير مقروءة</option>
                                <option value="read" {% if status == 'read' %}selected{% endif %}>مقروءة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة الرسائل -->
            <div class="card">
                <div class="card-body">
                    {% if messages.items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr class="text-center">
                                        <th width="5%">الحالة</th>
                                        <th width="20%">المرسل</th>
                                        <th width="35%">الموضوع</th>
                                        <th width="8%">النوع</th>
                                        <th width="8%">الأولوية</th>
                                        <th width="20%">تاريخ الإرسال</th>
                                        <th width="5%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody >
                                    {% for message in messages.items %}
                                        <!-- تمييز المحادثات التي تحتوي على رسائل غير مقروءة -->
                                        <tr class="{% if message.has_unread_in_thread %}table-warning{% endif %}" data-message-id="{{ message.id }}">
                                            <td class="text-center">
                                                {% if message.has_unread_in_thread %}
                                                    <i class="fas fa-envelope text-primary" title="يحتوي على رسائل غير مقروءة"></i>
                                                {% else %}
                                                    <i class="fas fa-envelope-open text-muted" title="جميع الرسائل مقروءة"></i>
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                <div class="d-flex flex-column align-items-center">
                                                    <div class="rounded-circle mb-2 bg-primary text-white d-flex align-items-center justify-content-center"
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <strong>{{ message.sender.name }}</strong>
                                                    <small class="text-muted">{{ message.sender.username }}</small>
                                                </div>
                                            </td>
                                            <td class="text-start">
                                                <a href="{{ url_for('view_message', id=message.id) }}"
                                                   class="text-decoration-none">
                                                    <strong>{{ message.subject }}</strong>
                                                </a>
                                                {% if message.attachments %}
                                                    <i class="fas fa-paperclip text-muted ms-1" title="يحتوي على مرفقات"></i>
                                                {% endif %}

                                                <!-- عرض معلومات المحادثة المجمعة -->
                                                {% if message.grouped_replies %}
                                                    <div class="mt-1">
                                                        <span class="badge bg-info">
                                                            <i class="fas fa-comments me-1"></i>
                                                            {{ message.grouped_replies|length }} رد
                                                        </span>
                                                        {% if message.has_unread_in_thread %}
                                                            <span class="badge bg-danger ms-1">جديد</span>
                                                        {% endif %}
                                                    </div>
                                                    <!-- معاينة آخر رد -->
                                                    {% set latest_reply = message.grouped_replies|sort(attribute='created_at')|last %}
                                                    <div class="mt-1">
                                                        <small class="text-muted">
                                                            آخر رد من {{ latest_reply.sender.name }}:
                                                            {{ latest_reply.content[:50] }}{% if latest_reply.content|length > 50 %}...{% endif %}
                                                        </small>
                                                    </div>
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                {% if message.message_type == 'general' %}
                                                    <span class="badge bg-secondary">عامة</span>
                                                {% elif message.message_type == 'notification' %}
                                                    <span class="badge bg-info">إشعار</span>
                                                {% elif message.message_type == 'alert' %}
                                                    <span class="badge bg-warning">تنبيه</span>
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                {% if message.priority == 'high' %}
                                                    <span class="badge bg-danger">عالية</span>
                                                {% elif message.priority == 'normal' %}
                                                    <span class="badge bg-success">عادية</span>
                                                {% elif message.priority == 'low' %}
                                                    <span class="badge bg-secondary">منخفضة</span>
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                <small>
                                                    {{ message.latest_activity_time.strftime('%Y-%m-%d %H:%M') }}
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ url_for('view_message', id=message.id) }}" 
                                                       class="btn btn-outline-primary btn-sm" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    {% if current_user.has_permission('delete_message') %}
                                                        <form method="POST" action="{{ url_for('delete_message', id=message.id) }}" 
                                                              style="display: inline;" 
                                                              onsubmit="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')">
                                                            <button type="submit" class="btn btn-outline-danger btn-sm" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- التصفح -->
                        {% if messages.pages > 1 %}
                            <nav aria-label="تصفح الرسائل">
                                <ul class="pagination justify-content-center">
                                    {% if messages.has_prev %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('messages_inbox', page=messages.prev_num, search=search_query, type=message_type, status=status) }}">السابق</a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for page_num in messages.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != messages.page %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('messages_inbox', page=page_num, search=search_query, type=message_type, status=status) }}">{{ page_num }}</a>
                                                </li>
                                            {% else %}
                                                <li class="page-item active">
                                                    <span class="page-link">{{ page_num }}</span>
                                                </li>
                                            {% endif %}
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">...</span>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if messages.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('messages_inbox', page=messages.next_num, search=search_query, type=message_type, status=status) }}">التالي</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد رسائل</h5>
                            <p class="text-muted">لم يتم العثور على أي رسائل تطابق معايير البحث.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث عدد الرسائل غير المقروءة كل 30 ثانية
setInterval(function() {
    fetch('{{ url_for("get_unread_count") }}')
        .then(response => response.json())
        .then(data => {
            const badge = document.querySelector('.badge.bg-danger');
            if (data.count > 0) {
                if (badge) {
                    badge.textContent = data.count;
                } else {
                    // إضافة شارة جديدة إذا لم تكن موجودة
                    const title = document.querySelector('h2');
                    const newBadge = document.createElement('span');
                    newBadge.className = 'badge bg-danger ms-2';
                    newBadge.textContent = data.count;
                    title.appendChild(newBadge);
                }
            } else if (badge) {
                badge.remove();
            }
        })
        .catch(error => console.error('Error:', error));
}, 30000);
</script>
{% endblock %}

{% block scripts %}
<script>
// تحديث حالة المحادثة عند العودة من عرض الرسالة
document.addEventListener('DOMContentLoaded', function() {
    // إذا كان هناك معرف رسالة في URL (عند العودة من عرض الرسالة)
    const urlParams = new URLSearchParams(window.location.search);
    const messageId = urlParams.get('viewed');

    if (messageId) {
        // إزالة التمييز البصري من المحادثة
        const messageRow = document.querySelector(`tr[data-message-id="${messageId}"]`);
        if (messageRow) {
            messageRow.classList.remove('table-warning');
        }

        // تحديث عداد الرسائل غير المقروءة
        setTimeout(function() {
            updateUnreadMessagesCount();
        }, 1000);

        // إزالة المعامل من URL
        const newUrl = window.location.pathname + window.location.search.replace(/[?&]viewed=\d+/, '');
        window.history.replaceState({}, '', newUrl);
    }
});

function updateUnreadMessagesCount() {
    fetch('{{ url_for("get_unread_count") }}')
        .then(response => response.json())
        .then(data => {
            const badge = document.getElementById('unread-messages-badge');
            if (badge) {
                if (data.count > 0) {
                    badge.textContent = data.count;
                    badge.style.display = 'inline';
                    badge.title = `لديك ${data.count} رسالة غير مقروءة`;
                } else {
                    badge.style.display = 'none';
                }
            }
        })
        .catch(error => console.error('Error:', error));
}
</script>
{% endblock %}
