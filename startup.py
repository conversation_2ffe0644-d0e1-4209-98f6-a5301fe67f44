from flask import Flask
import socket
import sys
import os

def check_port(port):
    """التحقق من توفر المنفذ"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        sock.bind(('127.0.0.1', port))
        sock.close()
        return True
    except:
        sock.close()
        return False

def find_available_port():
    """البحث عن منفذ متاح"""
    ports = [5000, 8080, 3000, 8000]
    for port in ports:
        if check_port(port):
            return port
    return None

# إنشاء تطبيق Flask
app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <h1 style="text-align: center; margin-top: 50px;">مرحباً بك!</h1>
    <p style="text-align: center;">التطبيق يعمل بنجاح ✓</p>
    '''

if __name__ == '__main__':
    try:
        # البحث عن منفذ متاح
        port = find_available_port()
        if not port:
            print("خطأ: لم يتم العثور على منفذ متاح!")
            sys.exit(1)

        print("\n=== معلومات التشغيل ===")
        print(f"المسار الحالي: {os.getcwd()}")
        print(f"المنفذ المستخدم: {port}")
        print("\nجاري تشغيل التطبيق...")
        print(f"يمكنك الوصول إليه على: http://127.0.0.1:{port}")
        print("للإيقاف اضغط: CTRL+C\n")
        
        app.run(
            host='127.0.0.1',
            port=port,
            debug=True,
            use_reloader=False  # تعطيل إعادة التحميل التلقائي لتجنب المشاكل
        )
    except Exception as e:
        print(f"\nحدث خطأ: {str(e)}")
        print("\nالحلول المقترحة:")
        print("1. تأكد من عدم تشغيل نسخة أخرى من التطبيق")
        print("2. جرب إغلاق المتصفح وإعادة فتحه")
        print("3. تأكد من عدم وجود برامج أخرى تستخدم نفس المنفذ")
        sys.exit(1)
