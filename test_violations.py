#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام المخالفات
"""

from app import app, db, ViolationType, Violation

def test_violations():
    """اختبار نظام المخالفات"""
    
    with app.app_context():
        try:
            print("=== اختبار نظام المخالفات ===\n")
            
            # 1. اختبار استعلام أنواع المخالفات
            print("1. اختبار استعلام أنواع المخالفات...")
            types = ViolationType.query.order_by(ViolationType.name).all()
            print(f"✓ تم العثور على {len(types)} نوع مخالفة")
            
            for vtype in types:
                print(f"  - {vtype.name}: {vtype.penalty_amount} ريال")
            
            # 2. اختبار إنشاء نوع مخالفة جديد
            print("\n2. اختبار إنشاء نوع مخالفة جديد...")
            new_type = ViolationType(
                name="اختبار نوع مخالفة",
                description="هذا نوع مخالفة للاختبار",
                penalty_amount=15.0,
                is_active=True
            )
            
            db.session.add(new_type)
            db.session.commit()
            print("✓ تم إنشاء نوع مخالفة جديد بنجاح")
            
            # 3. اختبار تحديث نوع المخالفة
            print("\n3. اختبار تحديث نوع المخالفة...")
            new_type.penalty_amount = 20.0
            db.session.commit()
            print("✓ تم تحديث نوع المخالفة بنجاح")
            
            # 4. اختبار حذف نوع المخالفة
            print("\n4. اختبار حذف نوع المخالفة...")
            db.session.delete(new_type)
            db.session.commit()
            print("✓ تم حذف نوع المخالفة بنجاح")
            
            # 5. اختبار العلاقات
            print("\n5. اختبار العلاقات...")
            
            # التحقق من وجود موظفين
            from app import Employee
            employees = Employee.query.limit(3).all()
            
            if employees:
                print(f"تم العثور على {len(employees)} موظف للاختبار")
                
                # إنشاء مخالفة تجريبية
                violation = Violation(
                    employee_id=employees[0].id,
                    violation_type_id=types[0].id,
                    violation_date='2025-06-30',
                    description=f"مخالفة تجريبية للموظف {employees[0].name}",
                    penalty_amount=types[0].penalty_amount,
                    status='pending',
                    notes='مخالفة للاختبار',
                    created_by=1
                )
                
                db.session.add(violation)
                db.session.commit()
                print("✓ تم إنشاء مخالفة تجريبية بنجاح")
                
                # اختبار العلاقة
                print(f"  الموظف: {violation.employee.name}")
                print(f"  نوع المخالفة: {violation.violation_type.name}")
                print(f"  الغرامة: {violation.penalty_amount} ريال")
                
                # حذف المخالفة التجريبية
                db.session.delete(violation)
                db.session.commit()
                print("✓ تم حذف المخالفة التجريبية")
            else:
                print("لا توجد موظفين للاختبار")
            
            print("\n✅ جميع الاختبارات نجحت!")
            
        except Exception as e:
            print(f"❌ فشل الاختبار: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_violations()
