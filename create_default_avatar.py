from PIL import Image, ImageDraw
import os

# إنشاء صورة جديدة بخلفية رمادية
size = (128, 128)
img = Image.new('RGB', size, '#e9ecef')
draw = ImageDraw.Draw(img)

# رسم دائرة للرأس
head_center = (64, 48)
head_radius = 24
draw.ellipse([(head_center[0]-head_radius, head_center[1]-head_radius),
              (head_center[0]+head_radius, head_center[1]+head_radius)],
             fill='#adb5bd')

# رسم نصف دائرة للجسم
body_center = (64, 128)
body_radius = 48
draw.ellipse([(body_center[0]-body_radius, body_center[1]-body_radius),
              (body_center[0]+body_radius, body_center[1]+body_radius)],
             fill='#adb5bd')

# حفظ الصورة
profile_dir = os.path.join('static', 'img', 'profile')
if not os.path.exists(profile_dir):
    os.makedirs(profile_dir)

img.save(os.path.join(profile_dir, 'default.jpg'), 'JPEG')
print("تم إنشاء الصورة الافتراضية بنجاح!")
