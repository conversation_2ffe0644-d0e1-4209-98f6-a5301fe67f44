{% extends 'base.html' %}

{% block content %}
<div class="container-fluid py-4">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary mb-0"><i class="fas fa-chart-line me-2"></i> سجل المبيعات</h2>
        <div>
            <a href="{{ url_for('new_sales_target') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> تسجيل تارجيت شهري
            </a>
            <a href="{{ url_for('new_daily_sales') }}" class="btn btn-success ms-2">
                <i class="fas fa-plus-circle me-1"></i> إضافة مبيعات يومية
            </a>
        </div>
    </div>

    <!-- بحث وتصفية -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="get" action="{{ url_for('sales_clubs_list') }}">
                <div class="row align-items-end">
                    <div class="col-md-8">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="اسم النادي..." value="{{ search_query }}">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100 py-2 fs-5">تطبيق</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة النوادي -->
    <div class="card shadow-lg">
        <div class="card-header bg-white py-3">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <h4 class="mb-0 text-primary me-2"><i class="fas fa-list me-2"></i> قائمة الأندية</h4>
                    <span class="badge bg-primary rounded-pill">{{ clubs|length }}</span>
                </div>
                <span class="text-muted fs-5">سجل مبيعات الأندية لشهر ({{ formatted_month }})</span>
            </div>
        </div>
        <div class="card-body p-0">
            {% if clubs %}
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">#</th>
                            <th style="font-size: 18px; color: #ffffff; background-color: #0d6efd; font-weight: bold;">اسم النادي</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">التارجيت</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">المبيعات المحققة</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">نسبة المبيعات</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for club in clubs %}
                        <tr>
                            <td class="text-center">{{ loop.index }}</td>
                            <td>
                                <a href="{{ url_for('sales_club_detail', club_id=club.id) }}" class="text-decoration-none text-primary fw-bold" style="cursor: pointer;">
                                    {{ club.name }}
                                </a>
                            </td>
                            <td class="text-center">
                                {% if club.target_amount %}
                                    {{ "{:,.0f}".format(club.target_amount) }} ريال
                                {% else %}
                                    <span class="text-muted">لا يوجد تارجيت</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if club.achieved_amount %}
                                    {{ "{:,.0f}".format(club.achieved_amount) }} ريال
                                {% else %}
                                    <span class="text-muted">0 ريال</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 25px; position: relative;">
                                    {% set percentage = club.achievement_percentage %}
                                    <div class="progress-bar {% if percentage < 50 %}bg-danger{% elif percentage < 80 %}bg-warning{% else %}bg-success{% endif %}" role="progressbar" style="width: {{ percentage }}%; min-width: 2em;" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center;">
                                        <span style="color: #000000; font-weight: bold; font-size: 16px; z-index: 10;">{{ "%.1f"|format(percentage) }}%</span>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                <a href="{{ url_for('sales_club_detail', club_id=club.id) }}" class="btn btn-info">
                                    <i class="fas fa-eye me-1"></i> عرض المبيعات
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-chart-line fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أندية للعرض</h5>
                <p class="text-muted">قم بإضافة نادي جديد للبدء</p>
                <a href="{{ url_for('new_club') }}" class="btn btn-primary mt-2">
                    <i class="fas fa-plus-circle me-1"></i> إضافة نادي جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
