import sqlite3

def add_profile_image_column():
    # فتح اتصال بقاعدة البيانات
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        # حفظ البيانات القديمة
        cursor.execute('CREATE TABLE user_backup AS SELECT * FROM user')
        
        # حذف الجدول القديم
        cursor.execute('DROP TABLE user')
        
        # إنشاء الجدول الجديد بالعمود الجديد
        cursor.execute('''CREATE TABLE user (
            id INTEGER NOT NULL PRIMARY KEY,
            username VARCHAR(64),
            name VARCHAR(100),
            phone VARCHAR(20),
            password VARCHAR(128),
            is_admin BOOLEAN,
            role VARCHAR(50) DEFAULT 'user',
            profile_image VARCHAR(255) DEFAULT 'default.jpg'
        )''')
        
        # إعادة البيانات القديمة
        cursor.execute('''INSERT INTO user (id, username, name, phone, password, is_admin, role)
            SELECT id, username, name, phone, password, is_admin, role
            FROM user_backup''')
        
        # حذف الجدول المؤقت
        cursor.execute('DROP TABLE user_backup')
        
        # حفظ التغييرات
        conn.commit()
        print("تم إضافة عمود profile_image بنجاح!")
        
    except Exception as e:
        print(f"حدث خطأ: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    add_profile_image_column()
