#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت للتحقق من قاعدة البيانات الرئيسية
"""

import sqlite3
import os

def check_main_database():
    """التحقق من قاعدة البيانات الرئيسية"""
    
    # البحث عن ملفات قاعدة البيانات
    db_files = []
    for file in os.listdir('.'):
        if file.endswith('.db'):
            db_files.append(file)
    
    print("ملفات قاعدة البيانات الموجودة:")
    for db_file in db_files:
        print(f"  - {db_file}")
    
    if not db_files:
        print("لا توجد ملفات قاعدة بيانات!")
        return
    
    # فحص كل ملف قاعدة بيانات
    for db_file in db_files:
        print(f"\n{'='*50}")
        print(f"فحص قاعدة البيانات: {db_file}")
        print(f"{'='*50}")
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # الحصول على قائمة الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"عدد الجداول: {len(tables)}")
            if tables:
                print("الجداول:")
                for table in tables:
                    table_name = table[0]
                    if table_name != 'sqlite_sequence':
                        # عدد السجلات في كل جدول
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        print(f"  - {table_name} ({count} سجل)")
            
            # البحث عن جداول محددة
            important_tables = ['employee', 'violation', 'violation_type', 'user', 'club']
            print(f"\nالجداول المهمة:")
            for table in important_tables:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                exists = cursor.fetchone()
                if exists:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"  ✓ {table} ({count} سجل)")
                else:
                    print(f"  ✗ {table} (غير موجود)")
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في فحص {db_file}: {e}")

if __name__ == "__main__":
    check_main_database()
