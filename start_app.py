from app import app
import os
import logging
from logging.handlers import RotatingFileHandler

if __name__ == "__main__":
    # إعداد السجلات للتشخيص
    if not os.path.exists('logs'):
        os.mkdir('logs')
    
    file_handler = RotatingFileHandler('logs/app.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('تشغيل التطبيق')
    
    print("\nجاري تشغيل التطبيق...")
    print("يمكنك الوصول إلى التطبيق على:")
    print("http://localhost:5000")
    print("http://127.0.0.1:5000")
    print("للإيقاف اضغط: CTRL+C\n")

    try:
        app.run(host='127.0.0.1', port=5000, debug=True)
    except OSError as e:
        if "Address already in use" in str(e):
            print("\nالمنفذ 5000 مشغول، جاري المحاولة على المنفذ 8080...")
            try:
                app.run(host='127.0.0.1', port=8080, debug=True)
            except Exception as e2:
                print(f"\nفشل تشغيل التطبيق: {str(e2)}")
                print("\nحلول مقترحة:")
                print("1. أغلق أي نسخة سابقة من التطبيق")
                print("2. تأكد من عدم تشغيل أي تطبيق آخر على المنفذ 5000 أو 8080")
                print("3. انتظر دقيقة ثم حاول مرة أخرى")
                print("4. أعد تشغيل الكمبيوتر")
        else:
            print(f"\nفشل تشغيل التطبيق: {str(e)}")
