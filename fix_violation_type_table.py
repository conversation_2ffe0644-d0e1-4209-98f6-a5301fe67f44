#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لإصلاح جدول violation_type في قاعدة البيانات
"""

import sqlite3
from datetime import datetime

def fix_violation_type_table():
    """إصلاح جدول violation_type بإضافة الأعمدة المفقودة"""
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('area_manager.db')
    cursor = conn.cursor()
    
    try:
        print("جاري فحص جدول violation_type...")
        
        # التحقق من وجود الجدول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='violation_type'
        """)
        
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("الجدول موجود، جاري فحص الأعمدة...")
            
            # الحصول على معلومات الأعمدة الحالية
            cursor.execute("PRAGMA table_info(violation_type)")
            columns_info = cursor.fetchall()
            columns = [column[1] for column in columns_info]
            print(f"الأعمدة الحالية: {columns}")
            
            # إذا كان الجدول يحتوي على الأعمدة الجديدة، لا نحتاج لفعل شيء
            if 'penalty_amount' in columns:
                print("الجدول محدث بالفعل!")
                return
            
            # إنشاء جدول جديد بالهيكل الصحيح
            print("إنشاء جدول جديد بالهيكل الصحيح...")
            cursor.execute("""
                CREATE TABLE violation_type_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    penalty_amount FLOAT DEFAULT 0.0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # نسخ البيانات الموجودة إلى الجدول الجديد
            print("نسخ البيانات الموجودة...")
            if 'name' in columns:
                cursor.execute("""
                    INSERT INTO violation_type_new (id, name, description, penalty_amount, is_active)
                    SELECT id, name, 
                           CASE WHEN EXISTS(SELECT 1 FROM pragma_table_info('violation_type') WHERE name='description') 
                                THEN description ELSE '' END,
                           0.0,
                           CASE WHEN EXISTS(SELECT 1 FROM pragma_table_info('violation_type') WHERE name='is_active') 
                                THEN is_active ELSE 1 END
                    FROM violation_type
                """)
            
            # حذف الجدول القديم
            print("حذف الجدول القديم...")
            cursor.execute("DROP TABLE violation_type")
            
            # إعادة تسمية الجدول الجديد
            print("إعادة تسمية الجدول الجديد...")
            cursor.execute("ALTER TABLE violation_type_new RENAME TO violation_type")
            
        else:
            print("الجدول غير موجود، إنشاء جدول جديد...")
            cursor.execute("""
                CREATE TABLE violation_type (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    penalty_amount FLOAT DEFAULT 0.0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
        
        # التحقق من وجود بيانات في الجدول
        cursor.execute("SELECT COUNT(*) FROM violation_type")
        count = cursor.fetchone()[0]
        
        if count == 0:
            print("إضافة بيانات افتراضية...")
            default_types = [
                ('تأخير عن العمل', 'التأخير عن موعد بداية العمل', 50.0),
                ('عدم ارتداء الزي الموحد', 'عدم الالتزام بالزي الموحد للعمل', 25.0),
                ('استخدام الهاتف أثناء العمل', 'استخدام الهاتف الشخصي أثناء ساعات العمل', 30.0),
                ('عدم تنظيف المنطقة المخصصة', 'عدم الحفاظ على نظافة منطقة العمل', 40.0),
                ('التعامل غير اللائق مع العملاء', 'سوء التعامل مع العملاء', 100.0)
            ]
            
            for name, desc, penalty in default_types:
                cursor.execute("""
                    INSERT INTO violation_type (name, description, penalty_amount, is_active)
                    VALUES (?, ?, ?, 1)
                """, (name, desc, penalty))
            
            print("تم إضافة البيانات الافتراضية")
        
        # حفظ التغييرات
        conn.commit()
        print("تم إصلاح جدول violation_type بنجاح!")
        
        # عرض البيانات الحالية
        cursor.execute("SELECT * FROM violation_type")
        rows = cursor.fetchall()
        print(f"\nعدد أنواع المخالفات: {len(rows)}")
        for row in rows:
            print(f"- {row[1]} (غرامة: {row[3]} ريال)")
        
        # التحقق من الهيكل النهائي
        cursor.execute("PRAGMA table_info(violation_type)")
        final_columns = cursor.fetchall()
        print(f"\nالأعمدة النهائية:")
        for col in final_columns:
            print(f"  - {col[1]} ({col[2]})")
        
    except Exception as e:
        print(f"حدث خطأ: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    fix_violation_type_table()
