import socket
import subprocess
import sys
import os
import platform
import psutil
import requests

def check_port_in_use(port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def check_localhost_resolves():
    try:
        return socket.gethostbyname('localhost')
    except:
        return None

def check_local_server(port):
    try:
        response = requests.get(f'http://127.0.0.1:{port}', timeout=2)
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        return False

def print_network_info():
    print("\n=== معلومات الشبكة ===")
    
    # التحقق من localhost
    localhost_ip = check_localhost_resolves()
    print(f"عنوان localhost: {localhost_ip or 'غير متوفر'}")
    
    # التحقق من المنافذ
    for port in [5000, 8080, 3000]:
        in_use = check_port_in_use(port)
        print(f"المنفذ {port}: {'مشغول' if in_use else 'متاح'}")
    
    # فحص عمليات Python
    python_processes = [p for p in psutil.process_iter(['pid', 'name', 'cmdline']) 
                       if 'python' in p.info['name'].lower()]
    if python_processes:
        print("\nعمليات Python النشطة:")
        for proc in python_processes:
            print(f"PID: {proc.info['pid']} - Command: {' '.join(proc.info['cmdline'] or [])}")
    else:
        print("\nلا توجد عمليات Python نشطة")

def main():
    print("=== أداة تشخيص خادم Flask ===")
    print(f"نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version.split()[0]}")
    
    print_network_info()
    
    print("\nجاري محاولة تشغيل خادم اختبار...")
    test_port = 5000
    
    if check_port_in_use(test_port):
        print(f"تحذير: المنفذ {test_port} مشغول حالياً!")
        print("جاري محاولة إيقاف العمليات...")
        os.system('taskkill /F /IM python.exe')
    
    print("\nالخطوات المقترحة:")
    print("1. تأكد من إضافة Python إلى استثناءات جدار الحماية")
    print("2. تحقق من ملف hosts (C:\\Windows\\System32\\drivers\\etc\\hosts)")
    print("3. جرب تشغيل البرنامج كمسؤول")
    print("4. جرب تعطيل جدار الحماية مؤقتاً للاختبار")

if __name__ == "__main__":
    main()
