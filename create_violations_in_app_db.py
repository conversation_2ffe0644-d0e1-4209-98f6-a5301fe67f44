#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لإنشاء جداول المخالفات في قاعدة البيانات الرئيسية app.db
"""

import sqlite3
from datetime import datetime

def create_violations_tables():
    """إنشاء جداول المخالفات في app.db"""
    
    # الاتصال بقاعدة البيانات الرئيسية
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        print("=== إنشاء جداول المخالفات في app.db ===\n")
        
        # 1. إنشاء جدول violation_type
        print("1. إنشاء جدول violation_type...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='violation_type'")
        if not cursor.fetchone():
            cursor.execute("""
                CREATE TABLE violation_type (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    penalty_amount FLOAT DEFAULT 0.0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✓ تم إنشاء جدول violation_type")
            
            # إضافة أنواع المخالفات الافتراضية
            default_types = [
                ('تأخير عن العمل', 'التأخير عن موعد بداية العمل', 50.0),
                ('عدم ارتداء الزي الموحد', 'عدم الالتزام بالزي الموحد للعمل', 25.0),
                ('استخدام الهاتف أثناء العمل', 'استخدام الهاتف الشخصي أثناء ساعات العمل', 30.0),
                ('عدم تنظيف المنطقة المخصصة', 'عدم الحفاظ على نظافة منطقة العمل', 40.0),
                ('التعامل غير اللائق مع العملاء', 'سوء التعامل مع العملاء', 100.0),
                ('عدم الالتزام بمواعيد الاستراحة', 'تجاوز وقت الاستراحة المحدد', 20.0),
                ('عدم اتباع إجراءات السلامة', 'عدم الالتزام بقواعد السلامة والأمان', 75.0),
                ('الغياب بدون إذن', 'الغياب عن العمل بدون إذن مسبق', 100.0)
            ]
            
            for name, desc, penalty in default_types:
                cursor.execute("""
                    INSERT INTO violation_type (name, description, penalty_amount, is_active)
                    VALUES (?, ?, ?, 1)
                """, (name, desc, penalty))
            
            print(f"✓ تم إضافة {len(default_types)} نوع مخالفة افتراضي")
        else:
            print("✓ جدول violation_type موجود بالفعل")
        
        # 2. إنشاء جدول violation
        print("\n2. إنشاء جدول violation...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='violation'")
        if not cursor.fetchone():
            cursor.execute("""
                CREATE TABLE violation (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    violation_type_id INTEGER NOT NULL,
                    violation_date DATE NOT NULL,
                    description TEXT,
                    penalty_amount FLOAT DEFAULT 0.0,
                    status VARCHAR(20) DEFAULT 'pending',
                    notes TEXT,
                    created_by INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES employee (id),
                    FOREIGN KEY (violation_type_id) REFERENCES violation_type (id),
                    FOREIGN KEY (created_by) REFERENCES user (id)
                )
            """)
            print("✓ تم إنشاء جدول violation")
        else:
            print("✓ جدول violation موجود بالفعل")
        
        # 3. إضافة بيانات تجريبية
        print("\n3. إضافة بيانات تجريبية...")
        
        # التحقق من وجود موظفين
        cursor.execute("SELECT id, name FROM employee LIMIT 10")
        employees = cursor.fetchall()
        
        if employees:
            print(f"تم العثور على {len(employees)} موظف")
            
            # التحقق من وجود مخالفات
            cursor.execute("SELECT COUNT(*) FROM violation")
            existing_violations = cursor.fetchone()[0]
            
            if existing_violations == 0:
                # إضافة مخالفات تجريبية
                test_violations = [
                    (employees[0][0], 1, '2025-06-25', f'تأخر الموظف {employees[0][1]} عن العمل لمدة 30 دقيقة', 50.0, 'pending', 'مخالفة أولى', 1),
                    (employees[0][0], 2, '2025-06-20', f'لم يرتدي الموظف {employees[0][1]} الزي الموحد', 25.0, 'resolved', 'تم التنبيه', 1),
                ]
                
                if len(employees) > 1:
                    test_violations.extend([
                        (employees[1][0], 3, '2025-06-22', f'استخدم الموظف {employees[1][1]} الهاتف أثناء العمل', 30.0, 'pending', 'تم رصده من قبل المشرف', 1),
                        (employees[1][0], 1, '2025-06-18', f'تأخر الموظف {employees[1][1]} عن العمل', 50.0, 'resolved', 'دفع الغرامة', 1),
                    ])
                
                if len(employees) > 2:
                    test_violations.extend([
                        (employees[2][0], 4, '2025-06-15', f'لم ينظف الموظف {employees[2][1]} منطقة عمله', 40.0, 'pending', 'تحتاج متابعة', 1),
                        (employees[2][0], 5, '2025-06-10', f'تعامل الموظف {employees[2][1]} بشكل غير لائق مع العملاء', 100.0, 'under_review', 'قيد المراجعة', 1),
                    ])
                
                for violation in test_violations:
                    cursor.execute("""
                        INSERT INTO violation (employee_id, violation_type_id, violation_date, description, 
                                             penalty_amount, status, notes, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, violation)
                
                print(f"✓ تم إضافة {len(test_violations)} مخالفة تجريبية")
            else:
                print(f"يوجد بالفعل {existing_violations} مخالفة في النظام")
        else:
            print("لا توجد موظفين في قاعدة البيانات")
        
        # حفظ التغييرات
        conn.commit()
        print("\n✓ تم حفظ جميع التغييرات بنجاح")
        
        # 4. التحقق من النتائج
        print("\n=== التحقق من النتائج ===")
        
        # عدد أنواع المخالفات
        cursor.execute("SELECT COUNT(*) FROM violation_type")
        vt_count = cursor.fetchone()[0]
        print(f"عدد أنواع المخالفات: {vt_count}")
        
        # عدد المخالفات
        cursor.execute("SELECT COUNT(*) FROM violation")
        v_count = cursor.fetchone()[0]
        print(f"عدد المخالفات: {v_count}")
        
        # اختبار العلاقة
        cursor.execute("""
            SELECT v.id, v.description, vt.name, vt.penalty_amount, e.name as employee_name
            FROM violation v
            JOIN violation_type vt ON v.violation_type_id = vt.id
            JOIN employee e ON v.employee_id = e.id
            LIMIT 3
        """)
        
        results = cursor.fetchall()
        if results:
            print("\n✓ العلاقات تعمل بشكل صحيح:")
            for result in results:
                print(f"  مخالفة {result[0]}: {result[4]} - {result[2]} - غرامة: {result[3]} ريال")
        
        # عرض أنواع المخالفات
        cursor.execute("SELECT name, penalty_amount FROM violation_type ORDER BY name")
        types = cursor.fetchall()
        print(f"\nأنواع المخالفات المتاحة:")
        for vtype in types:
            print(f"  - {vtype[0]} (غرامة: {vtype[1]} ريال)")
        
    except Exception as e:
        print(f"حدث خطأ: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    create_violations_tables()
