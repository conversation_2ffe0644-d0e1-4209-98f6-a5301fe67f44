{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">تعديل تقييم Google Maps - {{ club.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('google_maps_ratings') }}">تقييم النادي - Google Maps</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('google_maps_ratings_club', club_id=club.id) }}">{{ club.name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">تعديل تقييم</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('google_maps_ratings_club', club_id=club.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى تقييمات النادي
        </a>
    </div>
</div>

<!-- معلومات النادي -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-building me-2"></i> معلومات النادي</h5>
    </div>
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-3">
                <p class="mb-0"><strong>اسم النادي:</strong> {{ club.name }}</p>
            </div>
            {% if club.manager_name %}
            <div class="col-md-3">
                <p class="mb-0"><strong>مدير النادي:</strong> {{ club.manager_name }}</p>
            </div>
            {% endif %}
            {% if club.location %}
            <div class="col-md-3">
                <p class="mb-0"><strong>الموقع:</strong> {{ club.location }}</p>
            </div>
            {% endif %}
            {% if club.phone %}
            <div class="col-md-3">
                <p class="mb-0"><strong>رقم الهاتف:</strong> {{ club.phone }}</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نموذج تعديل التقييم -->
<div class="card shadow-sm">
    <div class="card-header bg-warning text-dark py-3">
        <h5 class="mb-0"><i class="fas fa-edit me-2"></i> تعديل تقييم Google Maps</h5>
    </div>
    <div class="card-body">
        <form method="POST" novalidate>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="date" class="form-label">
                            <i class="fas fa-calendar me-1"></i> التاريخ <span class="text-danger">*</span>
                        </label>
                        <input type="date" class="form-control" id="date" name="date" 
                               value="{{ rating.date.strftime('%Y-%m-%d') }}" required>
                        <div class="form-text">تاريخ تسجيل التقييم</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="rating_count" class="form-label">
                            <i class="fas fa-users me-1"></i> عدد التقييمات <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control english-number force-english-numbers" id="rating_count" name="rating_count"
                               min="0" step="1" value="{{ rating.rating_count }}" required placeholder="مثال: 150">
                        <div class="form-text">العدد الإجمالي للأشخاص الذين قيموا النادي</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="rating_average" class="form-label">
                            <i class="fas fa-star me-1"></i> نسبة التقييم <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control english-number force-english-numbers" id="rating_average" name="rating_average"
                               min="0" max="5" step="0.1" value="{{ rating.rating_average }}" required placeholder="مثال: 4.5">
                        <div class="form-text">متوسط تقييم النادي على Google Maps (من 0 إلى 5)</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">معاينة التقييم</label>
                        <div class="border rounded p-3 bg-light">
                            <div id="rating-preview" class="d-flex align-items-center">
                                <span id="rating-value" class="me-2 fw-bold force-english-numbers">{{ rating.rating_average }}</span>
                                <div id="rating-stars" class="text-warning"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('google_maps_ratings_club', club_id=club.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i> حفظ التعديلات
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="mt-4">
    <div class="alert alert-info">
        <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i> إرشادات تعديل التقييم:</h5>
        <ul class="mb-0">
            <li>تأكد من إدخال التاريخ الصحيح للتقييم</li>
            <li>أدخل عدد التقييمات من Google Maps</li>
            <li>أدخل نسبة التقييم كما تظهر على Google Maps (من 0 إلى 5)</li>
            <li>لا يمكن إضافة أكثر من تقييم واحد لنفس النادي في نفس التاريخ</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ratingAverageInput = document.getElementById('rating_average');
    const ratingValue = document.getElementById('rating-value');
    const ratingStars = document.getElementById('rating-stars');

    // التحقق من صحة النموذج قبل الإرسال
    document.querySelector('form').addEventListener('submit', function(e) {
        const dateValue = document.getElementById('date').value;
        if (!dateValue) {
            e.preventDefault();
            alert('الرجاء اختيار تاريخ صحيح');
            dateInput.focus();
            return false;
        }

        const rating = parseFloat(ratingAverageInput.value);
        if (rating < 0 || rating > 5) {
            e.preventDefault();
            alert('يجب أن تكون نسبة التقييم بين 0 و 5');
            ratingAverageInput.focus();
            return false;
        }

        const ratingCount = parseInt(document.getElementById('rating_count').value);
        if (ratingCount < 0) {
            e.preventDefault();
            alert('يجب أن يكون عدد التقييمات أكبر من أو يساوي 0');
            document.getElementById('rating_count').focus();
            return false;
        }
    });

    function updateStarsPreview(rating) {
        ratingValue.textContent = rating.toFixed(1);
        
        let starsHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                starsHtml += '<i class="fas fa-star text-warning"></i>';
            } else if (i - 0.5 <= rating) {
                starsHtml += '<i class="fas fa-star-half-alt text-warning"></i>';
            } else {
                starsHtml += '<i class="far fa-star text-warning"></i>';
            }
        }
        ratingStars.innerHTML = starsHtml;
    }

    // تحديث معاينة النجوم عند تغيير نسبة التقييم
    ratingAverageInput.addEventListener('input', function() {
        const rating = parseFloat(this.value) || 0;
        updateStarsPreview(rating);
    });

    // تحديث معاينة النجوم عند تحميل الصفحة
    updateStarsPreview(parseFloat(ratingAverageInput.value) || 0);
});
</script>
{% endblock %}
