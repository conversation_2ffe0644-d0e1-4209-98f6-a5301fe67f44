#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت للتحقق من هيكل قاعدة البيانات
"""

import sqlite3

def check_database_structure():
    """التحقق من هيكل قاعدة البيانات"""
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('area_manager.db')
    cursor = conn.cursor()
    
    try:
        print("=== فحص هيكل قاعدة البيانات ===\n")
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"عدد الجداول: {len(tables)}")
        print("الجداول الموجودة:")
        for table in tables:
            print(f"  - {table[0]}")
        
        print("\n" + "="*50)
        
        # التحقق من جدول violation_type
        print("\n=== فحص جدول violation_type ===")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='violation_type'")
        violation_type_exists = cursor.fetchone()
        
        if violation_type_exists:
            print("✓ جدول violation_type موجود")
            
            # الحصول على معلومات الأعمدة
            cursor.execute("PRAGMA table_info(violation_type)")
            columns = cursor.fetchall()
            
            print(f"عدد الأعمدة: {len(columns)}")
            print("الأعمدة:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - {'PK' if col[5] else ''}")
            
            # التحقق من وجود البيانات
            cursor.execute("SELECT COUNT(*) FROM violation_type")
            count = cursor.fetchone()[0]
            print(f"\nعدد السجلات: {count}")
            
            if count > 0:
                cursor.execute("SELECT * FROM violation_type LIMIT 3")
                rows = cursor.fetchall()
                print("عينة من البيانات:")
                for row in rows:
                    print(f"  {row}")
        else:
            print("✗ جدول violation_type غير موجود")
        
        print("\n" + "="*50)
        
        # التحقق من جدول violation
        print("\n=== فحص جدول violation ===")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='violation'")
        violation_exists = cursor.fetchone()
        
        if violation_exists:
            print("✓ جدول violation موجود")
            
            # الحصول على معلومات الأعمدة
            cursor.execute("PRAGMA table_info(violation)")
            columns = cursor.fetchall()
            
            print(f"عدد الأعمدة: {len(columns)}")
            print("الأعمدة:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - {'PK' if col[5] else ''}")
            
            # التحقق من وجود البيانات
            cursor.execute("SELECT COUNT(*) FROM violation")
            count = cursor.fetchone()[0]
            print(f"\nعدد السجلات: {count}")
            
            if count > 0:
                cursor.execute("SELECT * FROM violation LIMIT 3")
                rows = cursor.fetchall()
                print("عينة من البيانات:")
                for row in rows:
                    print(f"  {row}")
        else:
            print("✗ جدول violation غير موجود")
        
        print("\n" + "="*50)
        
        # التحقق من المفاتيح الخارجية
        print("\n=== فحص المفاتيح الخارجية ===")
        if violation_exists:
            cursor.execute("PRAGMA foreign_key_list(violation)")
            fks = cursor.fetchall()
            if fks:
                print("المفاتيح الخارجية في جدول violation:")
                for fk in fks:
                    print(f"  - {fk[3]} -> {fk[2]}.{fk[4]}")
            else:
                print("لا توجد مفاتيح خارجية في جدول violation")
        
        print("\n" + "="*50)
        
        # محاولة تشغيل استعلام مشابه للذي يفشل في التطبيق
        print("\n=== اختبار الاستعلام المشكل ===")
        try:
            cursor.execute("""
                SELECT violation_type.id, violation_type.name, violation_type.description, 
                       violation_type.penalty_amount, violation_type.is_active
                FROM violation_type 
                ORDER BY violation_type.name
            """)
            rows = cursor.fetchall()
            print(f"✓ الاستعلام نجح - عدد النتائج: {len(rows)}")
            if rows:
                print("عينة من النتائج:")
                for row in rows[:3]:
                    print(f"  {row}")
        except Exception as e:
            print(f"✗ الاستعلام فشل: {e}")
        
    except Exception as e:
        print(f"حدث خطأ: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    check_database_structure()
