#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# إنشاء ملف Excel تجريبي لاختبار استيراد أنواع المخالفات
data = {
    'name': [
        'التأخير عن العمل',
        'عدم ارتداء الزي الموحد',
        'استخدام الهاتف أثناء العمل',
        'عدم تنظيف المعدات',
        'التغيب بدون إذن',
        'عدم احترام العملاء',
        'مخالفة قواعد السلامة',
        'عدم اتباع التعليمات',
        'التدخين في المكان المحظور',
        'عدم الالتزام بمواعيد الراحة'
    ],
    'description': [
        'التأخير عن موعد بداية العمل المحدد',
        'عدم ارتداء الزي الرسمي للنادي',
        'استخدام الهاتف الشخصي أثناء ساعات العمل',
        'عدم تنظيف الأجهزة والمعدات بعد الاستخدام',
        'التغيب عن العمل بدون إذن مسبق',
        'التعامل بطريقة غير لائقة مع العملاء',
        'عدم اتباع إجراءات السلامة والأمان',
        'عدم تنفيذ التعليمات الصادرة من الإدارة',
        'التدخين في الأماكن المحظورة داخل النادي',
        'عدم الالتزام بأوقات الراحة المحددة'
    ],
    'penalty_amount': [
        50.0,
        25.0,
        30.0,
        20.0,
        100.0,
        75.0,
        150.0,
        40.0,
        60.0,
        35.0
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(data)

# حفظ الملف
df.to_excel('test_violation_types.xlsx', index=False, engine='openpyxl')
print("تم إنشاء ملف test_violation_types.xlsx بنجاح!")
print(f"الملف يحتوي على {len(df)} نوع مخالفة")
print("\nمحتوى الملف:")
print(df.to_string(index=False))
