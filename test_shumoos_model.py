#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime, date
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إعداد التطبيق
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///app.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# نموذج النادي
class Club(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    location = db.Column(db.String(200))
    manager_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))

# نموذج المستخدم
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)

# نموذج شموس
class Shumoos(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    club_id = db.Column(db.Integer, db.ForeignKey('club.id'), nullable=False)
    registered_count = db.Column(db.Integer, nullable=False)
    registration_date = db.Column(db.Date, nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    club = db.relationship('Club', backref='shumoos_records')
    creator = db.relationship('User', backref='shumoos_records')

    def get_difference(self):
        previous_record = Shumoos.query.filter(
            Shumoos.club_id == self.club_id,
            Shumoos.registration_date < self.registration_date
        ).order_by(Shumoos.registration_date.desc()).first()
        if previous_record:
            return self.registered_count - previous_record.registered_count
        else:
            return self.registered_count

    def __repr__(self):
        return f'<Shumoos {self.id} - Club: {self.club_id} - Count: {self.registered_count}>'

if __name__ == '__main__':
    with app.app_context():
        try:
            # اختبار النموذج
            print('اختبار نموذج Shumoos...')
            print(f'نموذج Shumoos: {Shumoos}')
            print(f'حقول النموذج: {[c.name for c in Shumoos.__table__.columns]}')
            
            # اختبار الاستعلام
            shumoos_records = Shumoos.query.all()
            print(f'عدد سجلات شموس: {len(shumoos_records)}')
            
            print('تم اختبار النموذج بنجاح!')
            
        except Exception as e:
            print(f'خطأ في اختبار النموذج: {e}')
            import traceback
            traceback.print_exc()
