import socket
import sys
import os
import platform
import flask

def check_port(port):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = False
    try:
        sock.bind(("127.0.0.1", port))
        result = True
    except socket.error as e:
        print(f"خطأ في المنفذ {port}: {e}")
    finally:
        sock.close()
    return result

def print_system_info():
    print("\n=== معلومات النظام ===")
    print(f"نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"إصدار Python: {sys.version}")
    print(f"مسار Python: {sys.executable}")
    print(f"المسار الحالي: {os.getcwd()}")
    print(f"إصدار Flask: {flask.__version__}")
    print("=====================\n")

def main():
    print_system_info()
    
    print("اختبار المنافذ...")
    ports_to_test = [5000, 8080, 3000]
    available_ports = []
    
    for port in ports_to_test:
        if check_port(port):
            available_ports.append(port)
            print(f"المنفذ {port} متاح للاستخدام ✓")
        else:
            print(f"المنفذ {port} مشغول ✗")
    
    if available_ports:
        print(f"\nالمنافذ المتاحة: {available_ports}")
        print("يمكنك تعديل ملف app.py لاستخدام أحد هذه المنافذ")
    else:
        print("\nتحذير: جميع المنافذ المختبرة مشغولة!")

if __name__ == "__main__":
    main()
