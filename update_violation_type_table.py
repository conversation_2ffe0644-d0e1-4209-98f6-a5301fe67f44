#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لتحديث جدول violation_type في قاعدة البيانات
"""

import sqlite3
from datetime import datetime

def update_violation_type_table():
    """تحديث جدول violation_type بإضافة الأعمدة المفقودة"""
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('area_manager.db')
    cursor = conn.cursor()
    
    try:
        print("جاري تحديث جدول violation_type...")
        
        # التحقق من وجود الجدول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='violation_type'
        """)
        
        if not cursor.fetchone():
            print("إنشاء جدول violation_type جديد...")
            cursor.execute("""
                CREATE TABLE violation_type (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    penalty_amount FLOAT DEFAULT 0.0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إضافة بعض أنواع المخالفات الافتراضية
            default_types = [
                ('تأخير عن العمل', 'التأخير عن موعد بداية العمل', 50.0),
                ('عدم ارتداء الزي الموحد', 'عدم الالتزام بالزي الموحد للعمل', 25.0),
                ('استخدام الهاتف أثناء العمل', 'استخدام الهاتف الشخصي أثناء ساعات العمل', 30.0),
                ('عدم تنظيف المنطقة المخصصة', 'عدم الحفاظ على نظافة منطقة العمل', 40.0),
                ('التعامل غير اللائق مع العملاء', 'سوء التعامل مع العملاء', 100.0)
            ]
            
            for name, desc, penalty in default_types:
                cursor.execute("""
                    INSERT INTO violation_type (name, description, penalty_amount, is_active)
                    VALUES (?, ?, ?, 1)
                """, (name, desc, penalty))
            
            print("تم إنشاء جدول violation_type وإضافة البيانات الافتراضية")
        
        else:
            print("جدول violation_type موجود، جاري التحقق من الأعمدة...")
            
            # الحصول على معلومات الأعمدة الحالية
            cursor.execute("PRAGMA table_info(violation_type)")
            columns = [column[1] for column in cursor.fetchall()]
            print(f"الأعمدة الحالية: {columns}")
            
            # إضافة الأعمدة المفقودة
            columns_to_add = [
                ('description', 'TEXT'),
                ('penalty_amount', 'FLOAT DEFAULT 0.0'),
                ('is_active', 'BOOLEAN DEFAULT 1'),
                ('created_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'),
                ('updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
            ]
            
            for column_name, column_type in columns_to_add:
                if column_name not in columns:
                    try:
                        cursor.execute(f"ALTER TABLE violation_type ADD COLUMN {column_name} {column_type}")
                        print(f"تم إضافة العمود: {column_name}")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" not in str(e):
                            print(f"خطأ في إضافة العمود {column_name}: {e}")
                else:
                    print(f"العمود {column_name} موجود بالفعل")
            
            # التحقق من وجود بيانات في الجدول
            cursor.execute("SELECT COUNT(*) FROM violation_type")
            count = cursor.fetchone()[0]
            
            if count == 0:
                print("إضافة بيانات افتراضية...")
                default_types = [
                    ('تأخير عن العمل', 'التأخير عن موعد بداية العمل', 50.0),
                    ('عدم ارتداء الزي الموحد', 'عدم الالتزام بالزي الموحد للعمل', 25.0),
                    ('استخدام الهاتف أثناء العمل', 'استخدام الهاتف الشخصي أثناء ساعات العمل', 30.0),
                    ('عدم تنظيف المنطقة المخصصة', 'عدم الحفاظ على نظافة منطقة العمل', 40.0),
                    ('التعامل غير اللائق مع العملاء', 'سوء التعامل مع العملاء', 100.0)
                ]
                
                for name, desc, penalty in default_types:
                    cursor.execute("""
                        INSERT INTO violation_type (name, description, penalty_amount, is_active)
                        VALUES (?, ?, ?, 1)
                    """, (name, desc, penalty))
                
                print("تم إضافة البيانات الافتراضية")
        
        # حفظ التغييرات
        conn.commit()
        print("تم تحديث جدول violation_type بنجاح!")
        
        # عرض البيانات الحالية
        cursor.execute("SELECT * FROM violation_type")
        rows = cursor.fetchall()
        print(f"\nعدد أنواع المخالفات: {len(rows)}")
        for row in rows:
            print(f"- {row[1]} (غرامة: {row[3]} ريال)")
        
    except Exception as e:
        print(f"حدث خطأ: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    update_violation_type_table()
