{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">تقييمات Google Maps - {{ club.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('google_maps_ratings') }}">تقييم النادي - Google Maps</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ club.name }}</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('add_google_maps_rating', club_id=club.id) }}" class="btn btn-success me-2">
            <i class="fas fa-plus me-1"></i> إضافة تقييم جديد
        </a>
        <a href="{{ url_for('google_maps_ratings') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى القائمة
        </a>
    </div>
</div>

<!-- معلومات النادي -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-building me-2"></i> معلومات النادي</h5>
    </div>
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-3">
                <p class="mb-0"><strong>اسم النادي:</strong> {{ club.name }}</p>
            </div>
            {% if club.manager_name %}
            <div class="col-md-3">
                <p class="mb-0"><strong>مدير النادي:</strong> {{ club.manager_name }}</p>
            </div>
            {% endif %}
            {% if club.location %}
            <div class="col-md-3">
                <p class="mb-0"><strong>الموقع:</strong> {{ club.location }}</p>
            </div>
            {% endif %}
            {% if club.phone %}
            <div class="col-md-3">
                <p class="mb-0"><strong>رقم الهاتف:</strong> {{ club.phone }}</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- قائمة التقييمات -->
<div class="card shadow-sm">
    <div class="card-header bg-primary text-white py-3">
        <h5 class="mb-0"><i class="fas fa-star me-2"></i> سجل تقييمات Google Maps</h5>
    </div>
    <div class="card-body">
        {% if ratings %}
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th class="text-center" width="20%">التاريخ</th>
                        <th class="text-center" width="20%">عدد التقييمات</th>
                        <th class="text-center" width="25%">متوسط التقييم</th>
                        <th class="text-center" width="15%">تاريخ الإضافة</th>
                        <th class="text-center" width="15%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rating in ratings %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td class="text-center">
                            <strong>{{ rating.date.strftime('%Y-%m-%d') }}</strong>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-info fs-6 force-english-numbers">{{ rating.rating_count }}</span>
                        </td>
                        <td class="text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <span class="me-2 fw-bold force-english-numbers">{{ "%.1f"|format(rating.rating_average) }}</span>
                                <div class="text-warning">
                                    {% for i in range(5) %}
                                        {% if i < rating.rating_average %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            <small class="text-muted">{{ rating.created_at.strftime('%Y-%m-%d') }}</small>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('edit_google_maps_rating', rating_id=rating.id) }}" 
                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="POST" action="{{ url_for('delete_google_maps_rating', rating_id=rating.id) }}" 
                                      style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا التقييم؟')">
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- إحصائيات سريعة -->
        {% if ratings %}
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title">إجمالي التقييمات المسجلة</h5>
                        <h3 class="text-primary force-english-numbers">{{ ratings|length }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title">آخر عدد تقييمات</h5>
                        <h3 class="text-info force-english-numbers">{{ ratings[0].rating_count }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title">آخر متوسط تقييم</h5>
                        <h3 class="text-warning force-english-numbers">{{ "%.1f"|format(ratings[0].rating_average) }} <i class="fas fa-star"></i></h3>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-star fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد تقييمات مسجلة لهذا النادي</h5>
            <p class="text-muted">ابدأ بإضافة أول تقييم Google Maps للنادي</p>
            <a href="{{ url_for('add_google_maps_rating', club_id=club.id) }}" class="btn btn-success mt-2">
                <i class="fas fa-plus me-1"></i> إضافة تقييم جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
