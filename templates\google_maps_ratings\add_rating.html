{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إضافة تقييم Google Maps - {{ club.name }}</h1>
</div>

<div class="card">
    <div class="card-body">
        <form method="POST" action="{{ url_for('add_google_maps_rating', club_id=club.id) }}">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="date" class="form-label">
                            <i class="fas fa-calendar me-1"></i> التاريخ <span class="text-danger">*</span>
                        </label>
                        <input type="date" class="form-control" id="date" name="date" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="rating_count" class="form-label">
                            <i class="fas fa-users me-1"></i> عدد التقييمات <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control english-number force-english-numbers" id="rating_count" name="rating_count"
                               min="0" step="1" required placeholder="مثال: 150">
                        <div class="form-text">العدد الإجمالي للأشخاص الذين قيموا النادي</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="rating_average" class="form-label">
                            <i class="fas fa-star me-1"></i> نسبة التقييم <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control english-number force-english-numbers" id="rating_average" name="rating_average"
                               min="0" max="5" step="0.1" required placeholder="مثال: 4.5">
                        <div class="form-text">متوسط تقييم النادي على Google Maps (من 0 إلى 5)</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">معاينة التقييم</label>
                        <div class="border rounded p-3 bg-light">
                            <div id="rating-preview" class="d-flex align-items-center">
                                <span id="rating-value" class="me-2 fw-bold force-english-numbers">0.0</span>
                                <div id="rating-stars" class="text-warning">
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('google_maps_ratings_club', club_id=club.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i> حفظ التقييم
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="mt-4">
    <div class="alert alert-info">
        <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i> إرشادات إضافة التقييم:</h5>
        <ul class="mb-0">
            <li>تأكد من إدخال التاريخ الصحيح لتسجيل التقييم</li>
            <li>أدخل عدد التقييمات من Google Maps</li>
            <li>أدخل نسبة التقييم كما تظهر على Google Maps (من 0 إلى 5)</li>
            <li>لا يمكن إضافة أكثر من تقييم واحد لنفس النادي في نفس التاريخ</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('date');
    const ratingAverageInput = document.getElementById('rating_average');
    const ratingValue = document.getElementById('rating-value');
    const ratingStars = document.getElementById('rating-stars');

    // تعيين التاريخ الافتراضي
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const dd = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${yyyy}-${mm}-${dd}`;
    dateInput.value = formattedDate;
    dateInput.setAttribute('max', formattedDate);

    // التحقق من صحة النموذج قبل الإرسال
    document.querySelector('form').addEventListener('submit', function(e) {
        const dateValue = dateInput.value;
        if (!dateValue) {
            e.preventDefault();
            alert('الرجاء اختيار تاريخ صحيح');
            dateInput.focus();
            return false;
        }

        // التحقق من أن التاريخ ليس في المستقبل
        const selectedDate = new Date(dateValue);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (selectedDate > today) {
            e.preventDefault();
            alert('لا يمكن اختيار تاريخ في المستقبل');
            dateInput.focus();
            return false;
        }

        const rating = parseFloat(ratingAverageInput.value);
        if (rating < 0 || rating > 5) {
            e.preventDefault();
            alert('يجب أن تكون نسبة التقييم بين 0 و 5');
            ratingAverageInput.focus();
            return false;
        }

        const ratingCount = parseInt(document.getElementById('rating_count').value);
        if (ratingCount < 0) {
            e.preventDefault();
            alert('يجب أن يكون عدد التقييمات أكبر من أو يساوي 0');
            document.getElementById('rating_count').focus();
            return false;
        }
    });

    function updateStarsPreview(rating) {
        ratingValue.textContent = rating.toFixed(1);
        
        let starsHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                starsHtml += '<i class="fas fa-star text-warning"></i>';
            } else if (i - 0.5 <= rating) {
                starsHtml += '<i class="fas fa-star-half-alt text-warning"></i>';
            } else {
                starsHtml += '<i class="far fa-star text-warning"></i>';
            }
        }
        ratingStars.innerHTML = starsHtml;
    }

    // تحديث معاينة النجوم عند تغيير نسبة التقييم
    ratingAverageInput.addEventListener('input', function() {
        const rating = parseFloat(this.value) || 0;
        updateStarsPreview(rating);
    });

    // تحديث معاينة النجوم عند تحميل الصفحة
    updateStarsPreview(0);
});
</script>
{% endblock %}
