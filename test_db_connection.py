import sqlite3
import os

print("Testing database connection...")
db_path = 'app.db'

if os.path.exists(db_path):
    print(f"Database file exists: {db_path}")
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if club table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='club'")
        result = cursor.fetchone()
        
        if result:
            print("Club table exists")
            
            # Check current columns
            cursor.execute("PRAGMA table_info(club)")
            columns = cursor.fetchall()
            print("Current columns in club table:")
            for column in columns:
                print(f"  - {column[1]} ({column[2]})")
                
            # Check if target columns exist
            column_names = [column[1] for column in columns]
            target_columns = ['target_customer_service_count', 'target_trainer_count', 'target_worker_count']
            
            for col in target_columns:
                if col not in column_names:
                    print(f"Adding column: {col}")
                    try:
                        cursor.execute(f"ALTER TABLE club ADD COLUMN {col} INTEGER DEFAULT 0")
                        print(f"Successfully added: {col}")
                    except Exception as e:
                        print(f"Error adding {col}: {e}")
                else:
                    print(f"Column already exists: {col}")
            
            conn.commit()
            print("Database update completed!")
        else:
            print("Club table does not exist")
            
        conn.close()
    except Exception as e:
        print(f"Database error: {e}")
else:
    print(f"Database file not found: {db_path}")
