from app import app
import os
import logging
from logging.handlers import RotatingFileHandler

if __name__ == "__main__":
    # إعداد السجلات للتشخيص
    if not os.path.exists('logs'):
        os.mkdir('logs')
    
    file_handler = RotatingFileHandler('logs/app.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('تشغيل التطبيق')
    # تشغيل التطبيق الرئيسي
    print("\nجاري تشغيل التطبيق...")
    print("يمكنك الوصول إلى التطبيق على:")
    print("http://localhost:5000")
    print("http://127.0.0.1:5000")
    print("للإيقاف اضغط: CTRL+C\n")

    try:
        # حاول تشغيل التطبيق على المنفذ 5000
        app.run(
            host='0.0.0.0',  # للسماح بالوصول من أي عنوان IP محلي
            port=5000,
            debug=True
        )
    except Exception as e:
        print(f"\nخطأ في تشغيل التطبيق: {str(e)}")
        try:
            # إذا فشل المنفذ 5000، جرب المنفذ 8080
            print("\nجاري المحاولة على المنفذ 8080...")
            app.run(
                host='0.0.0.0',
                port=8080,
                debug=True
            )
        except Exception as e:
            print(f"\nفشل تشغيل التطبيق: {str(e)}")
            print("\nاقتراحات لحل المشكلة:")
            print("1. تأكد من إغلاق أي نسخة سابقة من التطبيق")
            print("2. أعد تشغيل الكمبيوتر")
            print("3. تحقق من إعدادات جدار الحماية")
