#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة حقول كثافة الموظفين إلى جدول النوادي
"""

import sqlite3
import os

def add_employee_target_columns():
    """إضافة حقول كثافة الموظفين إلى جدول النوادي"""
    
    # مسار قاعدة البيانات
    db_path = 'app.db'
    
    if not os.path.exists(db_path):
        print(f"ملف قاعدة البيانات غير موجود: {db_path}")
        return
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول النوادي
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='club'")
        if not cursor.fetchone():
            print("جدول النوادي غير موجود")
            return
        
        # التحقق من وجود الأعمدة الجديدة
        cursor.execute("PRAGMA table_info(club)")
        columns = [column[1] for column in cursor.fetchall()]
        
        columns_to_add = [
            ('target_customer_service_count', 'INTEGER DEFAULT 0'),
            ('target_trainer_count', 'INTEGER DEFAULT 0'),
            ('target_worker_count', 'INTEGER DEFAULT 0')
        ]
        
        # إضافة الأعمدة المفقودة
        for column_name, column_type in columns_to_add:
            if column_name not in columns:
                try:
                    cursor.execute(f"ALTER TABLE club ADD COLUMN {column_name} {column_type}")
                    print(f"تم إضافة العمود: {column_name}")
                except sqlite3.Error as e:
                    print(f"خطأ في إضافة العمود {column_name}: {e}")
            else:
                print(f"العمود {column_name} موجود بالفعل")
        
        # حفظ التغييرات
        conn.commit()
        print("تم إضافة حقول كثافة الموظفين بنجاح!")
        
        # عرض بنية الجدول المحدثة
        cursor.execute("PRAGMA table_info(club)")
        columns = cursor.fetchall()
        print("\nبنية جدول النوادي المحدثة:")
        for column in columns:
            print(f"  - {column[1]} ({column[2]})")
        
    except sqlite3.Error as e:
        print(f"خطأ في قاعدة البيانات: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    add_employee_target_columns()
