from app import app, db

if __name__ == '__main__':
    with app.app_context():
        try:
            db.create_all()
            print("تم تهيئة قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
    
    try:
        print("\nجاري تشغيل التطبيق...")
        print("يمكنك الوصول إلى التطبيق على:")
        print("http://127.0.0.1:5000")
        app.run(host='127.0.0.1', port=5000, debug=True)
    except Exception as e:
        print(f"\nخطأ في تشغيل التطبيق: {str(e)}")
        print("\nحلول محتملة:")
        print("1. تأكد من أن المنفذ 5000 غير مستخدم من قبل تطبيق آخر")
        print("2. أغلق جميع نوافذ المتصفح المفتوحة على التطبيق")
        print("3. انتظر دقيقة ثم حاول مرة أخرى")
